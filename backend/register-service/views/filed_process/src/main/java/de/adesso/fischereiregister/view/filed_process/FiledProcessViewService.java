package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.DeletionFlag;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.view.filed_process.eventhandling.FiledProcessViewHandler;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessType;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;

import java.util.List;
import java.util.UUID;

/**
 * This service handles the creation and retrieving of processes for the register file (Vorgangsakte).
 * <p>
 * The data of the events is passed into the corresponding methods by the {@link FiledProcessViewHandler},
 * so for each event a process is created. These processes can be retrieved for the register file for specific register
 * entry. In a register file, only the processes for the federal state of the requesting institution are shown.
 * </p>
 */
public interface FiledProcessViewService {

    void truncateView();

    /**
     * Get all processes for a register entry id filtered by the federal state.
     *
     * @param registerEntryId The register entry id for which the processes are retrieved.
     * @param federalState    The federal state to which the processes have to belong to.
     * @return A list of the found processes, can be empty.
     */
    List<FiledProcessView> getFiledProcesses(UUID registerEntryId, FederalState federalState);

    /**
     * Create and persist a process of type {@link FiledProcessType#QUALIFICATIONS_PROOF_CREATED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param person The personal data of the affected person.
     * @param qualificationsProof The proof that was created in this process The proof that was created in this process.
     */
    void createQualificationsProofCreatedProcess(ProcessHeaderData processHeaderData,
                                                 Person person,
                                                 QualificationsProof qualificationsProof);

    /**
     * Create and persist a process of type {@link FiledProcessType#JURISDICTION_CHANGED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param taxes The taxes that occurred with this process.
     * @param consentInfo The consent agreement the person gave for this process.
     * @param identificationDocuments The identificationDocument regarding this process.
     * @param jurisdiction The new jurisdiction the register entry is moved to.
     */
    void createJurisdictionMovedProcess(ProcessHeaderData processHeaderData,
                                        List<Tax> taxes,
                                        JurisdictionConsentInfo consentInfo,
                                        List<IdentificationDocument> identificationDocuments,
                                        Jurisdiction jurisdiction);

    /**
     * Create and persist a process of type {@link FiledProcessType#FISHING_LICENSE_CREATED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param person The personal data of the affected person.
     * @param serviceAccountId The id of the service account of the persons register entry.
     * @param taxes The taxes that occurred with this process.
     * @param fees The fees that are part of this process.
     * @param identificationDocuments The identificationDocument regarding this process.
     * @param fishingLicense The license created in this process.
     * @param officesAddress The office address the person wants the fishing license sent to.
     */
    void createFishingLicenseCreatedProcess(ProcessHeaderData processHeaderData,
                                            Person person,
                                            String serviceAccountId,
                                            List<Tax> taxes,
                                            List<Fee> fees,
                                            List<IdentificationDocument> identificationDocuments,
                                            FishingLicense fishingLicense,
                                            Address officesAddress);

    /**
     * Create and persist a process of type {@link FiledProcessType#FISHING_LICENSE_EXTENDED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param person The personal data of the affected person.
     * @param serviceAccountId The id of the service account of the persons register entry.
     * @param taxes The taxes that occurred with this process.
     * @param fees The fees that are part of this process.
     * @param identificationDocuments The identificationDocument regarding this process.
     * @param fishingLicenseNumber The number of the fishing license that is extended.
     * @param officesAddress The office address the person wants the fishing license sent to.
     */
    void createFishingLicenseExtendedProcess(ProcessHeaderData processHeaderData,
                                             Person person,
                                             String serviceAccountId,
                                             List<Tax> taxes,
                                             List<Fee> fees,
                                             List<IdentificationDocument> identificationDocuments,
                                             String fishingLicenseNumber,
                                             Address officesAddress);

    /**
     * Create and persist a process of type {@link FiledProcessType#REPLACEMENT_CARD_ORDERED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param person The personal data of the affected person.
     * @param serviceAccountId The id of the service account of the persons register entry.
     * @param taxes The taxes that occurred with this process.
     * @param fees The fees that are part of this process.
     * @param identificationDocuments The identificationDocument regarding this process.
     * @param fishingLicense The license for which the replacement card was ordered.
     * @param address The address the replacement card should be sent to.
     */
    void createReplacementCardOrderedProcess(ProcessHeaderData processHeaderData,
                                             Person person,
                                             String serviceAccountId,
                                             List<Tax> taxes,
                                             List<Fee> fees,
                                             List<IdentificationDocument> identificationDocuments,
                                             FishingLicense fishingLicense,
                                             Address address);

    /**
     * Create and persist a process of type {@link FiledProcessType#FISHING_TAX_CREATED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param person The personal data of the affected person.
     * @param serviceAccountId The id of the service account of the persons register entry.
     * @param taxes The taxes that occurred with this process.
     * @param consentInfo The consent agreement the person gave for this process.
     * @param identificationDocuments The identificationDocument regarding this process.
     */
    void createFishingTaxPayedProcess(ProcessHeaderData processHeaderData,
                                      Person person,
                                      String serviceAccountId,
                                      List<Tax> taxes,
                                      TaxConsentInfo consentInfo,
                                      List<IdentificationDocument> identificationDocuments);

    /**
     * Create and persist a process of type {@link FiledProcessType#BANNED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param ban The information of the ban that is issued.
     */
    void createBannedProcess(ProcessHeaderData processHeaderData, Ban ban);

    /**
     * Create and persist a process of type {@link FiledProcessType#UNBANNED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     */
    void createUnbannedProcess(ProcessHeaderData processHeaderData);

    /**
     * Create and persist a process of type {@link FiledProcessType#LIMITED_LICENSE_APPLICATION_CREATED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     * @param person The personal data of the affected person.
     * @param serviceAccountId The id of the service account of the persons register entry.
     * @param fees The fees that are part of this process.
     * @param consentInfo The consent agreement the person gave for this process.
     */
    void createLimitedLicenseApplicationProcess(ProcessHeaderData processHeaderData,
                                                Person person,
                                                String serviceAccountId,
                                                List<Fee> fees,
                                                ConsentInfo consentInfo);

    /**
     * Create and persist a process of type {@link FiledProcessType#LIMITED_LICENSE_APPLICATION_REJECTED}.
     * 
     * @param processHeaderData Contains the metadata of the process.
     */
    void createLimitedLicenseRejectedProcess(ProcessHeaderData processHeaderData);

    /**
     * Create and persist a process of type {@link FiledProcessType#MARKED_FOR_DELETION}.
     *
     * @param processHeaderData Contains the metadata of the process.
     */
    void createMarkedForDeletionProcess(ProcessHeaderData processHeaderData, DeletionFlag deletionFlag);
}
