package de.adesso.fischereiregister.view.errors_statistics.persistence;

import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table
@Getter
@Setter
public class ErrorsStatisticsView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String federalState;

    @Column()
    private String office;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ErrorType errorType;

    @Column(nullable = false)
    private int year;

    @Column(nullable = false)
    private int count;
}
