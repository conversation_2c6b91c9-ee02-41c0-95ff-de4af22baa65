package de.adesso.fischereiregister.view.errors_statistics.persistence;

import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ErrorsStatisticsViewRepository extends CrudRepository<ErrorsStatisticsView, Long> {

    @Query("""
            SELECT view FROM ErrorsStatisticsView view
            WHERE view.federalState = :federalState
              AND COALESCE(view.office, '') = COALESCE(:office, '')
              AND view.errorType = :errorType
              AND view.year = :year
            """)
    Optional<ErrorsStatisticsView> findByFederalStateAndOfficeAndErrorTypeAndYear(
            @Param("federalState") String federalState,
            @Param("office") String office,
            @Param("errorType") ErrorType errorType,
            @Param("year") int year
    );

    @Query("SELECT view FROM ErrorsStatisticsView view WHERE view.federalState = :federalState AND view.year IN :years")
    List<ErrorsStatisticsView> findByFederalStateAndYearIn(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM ErrorsStatisticsView view WHERE view.office = :office AND view.year IN :years")
    List<ErrorsStatisticsView> findByOfficeAndYearIn(
            @Param("office") String office,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM ErrorsStatisticsView view WHERE view.year IN :years")
    List<ErrorsStatisticsView> findByYearIn(@Param("years") List<Integer> years);

    @Query("SELECT DISTINCT view.year FROM ErrorsStatisticsView view ORDER BY view.year DESC")
    List<Integer> findDistinctYears();

    @Query("SELECT DISTINCT view.office FROM ErrorsStatisticsView view ORDER BY view.office")
    List<String> findDistinctOffices();

    @Query("SELECT DISTINCT view.office FROM ErrorsStatisticsView view WHERE view.year IN :years ORDER BY view.office")
    List<String> findDistinctOfficesByYears(@Param("years") List<Integer> years);

    @Query("SELECT DISTINCT view.office FROM ErrorsStatisticsView view WHERE view.federalState = :federalState ORDER BY view.office")
    List<String> findDistinctOfficesByFederalState(@Param("federalState") String federalState);

    @Query("SELECT DISTINCT view.office FROM ErrorsStatisticsView view WHERE view.federalState = :federalState AND view.year IN :years ORDER BY view.office")
    List<String> findDistinctOfficesByFederalStateAndYears(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );
}
