package de.adesso.fischereiregister.core.ports;

import java.util.List;

public interface ErrorsProtocolStatisticsPort {

    /**
     * Gets the count of online service errors for a specific year, federal state, and office
     */
    Integer getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(int year, String federalState, String office);

    /**
     * Gets the count of card order errors for a specific year, federal state, and office
     */
    Integer getCardOrderErrorsAmountByYearAndFederalStateAndOffice(int year, String federalState, String office);

    /**
     * Gets the count of system errors for a specific year, federal state, and office
     */
    Integer getSystemErrorsAmountByYearAndFederalStateAndOffice(int year, String federalState, String office);

    /**
     * Gets the count of online service errors for a specific year
     */
    Integer getOnlineServiceErrorsAmountByYear(int year);

    /**
     * Gets the count of card order errors for a specific year
     */
    Integer getCardOrderErrorsAmountByYear(int year);

    /**
     * Gets the count of system errors for a specific year
     */
    Integer getSystemErrorsAmountByYear(int year);

    /**
     * Gets all available years for which error statistics exist
     */
    List<Integer> getAvailableYears();
}
