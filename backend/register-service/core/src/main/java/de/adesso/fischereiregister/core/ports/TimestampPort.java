package de.adesso.fischereiregister.core.ports;

import java.time.Instant;
import java.util.UUID;

/**
 * Service interface for managing mocked timestamps during test data import.
 * This service maintains timestamps per register entry ID that can be used
 * instead of real timestamps during event handling when importing test data.
 */
public interface TimestampPort {

    /**
     * Pushes a mocked timestamp for a specific register entry ID.
     *
     * @param registerEntryId the register entry ID to associate the timestamp with
     * @param timestamp the timestamp to push
     */
    void pushMockedTimestamp(UUID registerEntryId, Instant timestamp);

    /**
     * Pushes a mocked timestamp parsed from a string for a specific register entry ID.
     * Supports formats: "dd.MM.yyyy HH:mm:ss" and "dd.MM.yyyy" (defaults to 12:00:00)
     *
     * @param registerEntryId the register entry ID to associate the timestamp with
     * @param timestampString the timestamp string to parse and push
     */
    void pushMockedTimestamp(UUID registerEntryId, String timestampString);

    /**
     * Pops the most recent mocked timestamp from the stack.
     *
     * @return the popped timestamp, or null if stack is empty
     * @deprecated Use {@link #popMockedTimestamp(UUID)} instead
     */
    @Deprecated
    Instant popMockedTimestamp();

    /**
     * Pops the most recent mocked timestamp for a specific register entry ID.
     *
     * @param registerEntryId the register entry ID to pop the timestamp for
     * @return the popped timestamp, or null if no timestamps exist for this register entry ID
     */
    Instant popMockedTimestamp(UUID registerEntryId);

    /**
     * Clears all mocked timestamps from the stack.
     */
    void clearMockedTimestamps();

    /**
     * Gets the appropriate timestamp to use in event handlers.
     * Returns the mocked timestamp if available, otherwise returns the provided real timestamp.
     *
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available, otherwise the real timestamp
     * @deprecated Use {@link #getEffectiveTimestamp(UUID, Instant)} instead
     */
    @Deprecated
    Instant getEffectiveTimestamp(Instant realTimestamp);

    /**
     * Gets the appropriate timestamp to use in event handlers for a specific register entry ID.
     * Returns the mocked timestamp if available, otherwise returns the provided real timestamp.
     *
     * @param registerEntryId the register entry ID to get the timestamp for
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available, otherwise the real timestamp
     */
    Instant getEffectiveTimestamp(UUID registerEntryId, Instant realTimestamp);
}
