package de.adesso.fischereiregister.errors_protocol.persistence;

import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for managing Protocol entities.
 * This interface extends CrudRepository to provide basic CRUD operations.
 */
@Component
@Repository
public interface ErrorProtocolRepository extends CrudRepository<ErrorProtocol, UUID> {


    /** Finds an ErrorProtocol by error type, federal state, and year.
     *
     * @param errorType      The type of error to search for.
     * @param federalState   The federal state to filter by.
     * @param year           The year to filter by.
     * @return An Optional containing the ErrorProtocol if found, otherwise empty.
     */
    @Query("""
             SELECT ep
             FROM ErrorProtocol ep
             WHERE ep.errorType = :errorType
               AND ep.federalState = :federalState
               AND ep.year = :year
            """)
    Optional<ErrorProtocol> findByErrorTypeAndFederalStateAndYear(
            @Param("errorType") ErrorType errorType,
            @Param("federalState") String federalState,
            @Param("year") int year);

    @Query("""
             SELECT ep
             FROM ErrorProtocol ep
             WHERE ep.errorType = :errorType
               AND ep.federalState = :federalState
               AND ep.year = :year
               AND coalesce(ep.office, "") = :office
            """)
    Optional<ErrorProtocol> findByErrorTypeAndFederalStateAndYearAndOffice(
            @Param("errorType") ErrorType errorType,
            @Param("federalState") String federalState,
            @Param("year") int year,
            @Param("office") String office);

    /**
     * Gets the count of errors for a specific error type, year, federal state, and office
     * This is a generic method that can be used internally to reduce code duplication
     * office is an optional parameter, if it is null, all offices are considered
     */
    @Query("""
            SELECT COALESCE(SUM(ep.count), 0)
            FROM ErrorProtocol ep
            WHERE ep.errorType = :errorType
              AND ep.year = :year
              AND ep.federalState = :federalState
              AND COALESCE(ep.office, "") = COALESCE(:office, "")
            """)
    Integer getErrorsAmountByTypeAndYearAndFederalStateAndOffice(
            @Param("errorType") ErrorType errorType,
            @Param("year") int year,
            @Param("federalState") String federalState,
            @Param("office") String office);

    /**
     * Gets the count of errors for a specific error type and year
     * This is a generic method that can be used internally to reduce code duplication
     */
    @Query("""
            SELECT COALESCE(SUM(ep.count), 0)
            FROM ErrorProtocol ep
            WHERE ep.errorType = :errorType
              AND ep.year = :year
            """)
    Integer getErrorsAmountByTypeAndYear(
            @Param("errorType") ErrorType errorType,
            @Param("year") int year);

    /**
     * Retrieves a list of distinct years from the ErrorProtocol table.
     *
     * @return A list of distinct years in descending order.
     */
    @Query("""
             SELECT DISTINCT ep.year
             FROM ErrorProtocol ep
                ORDER BY ep.year DESC
            """)
    List<Integer> selectYears();
}
