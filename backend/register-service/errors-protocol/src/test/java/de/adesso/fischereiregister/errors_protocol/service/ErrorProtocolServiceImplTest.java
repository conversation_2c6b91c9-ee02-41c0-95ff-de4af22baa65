package de.adesso.fischereiregister.errors_protocol.service;


import de.adesso.fischereiregister.errors_protocol.persistence.ErrorProtocol;
import de.adesso.fischereiregister.errors_protocol.persistence.ErrorProtocolRepository;
import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ErrorProtocolServiceImplTest {

    @Mock
    private ErrorProtocolRepository errorProtocolRepository;

    private ErrorProtocolServiceImpl errorProtocolService;

    @BeforeEach
    void setUp() {
        errorProtocolService = new ErrorProtocolServiceImpl(errorProtocolRepository);
    }

    @Test
    void shouldReportCardOrdersErrorAndCreateNewEntry() {
        // given
        String office = "Office1";
        String federalState = "State1";
        int year = 2023;
        ErrorType errorType = ErrorType.CARD_ORDER_ERROR;

        when(errorProtocolRepository.findByErrorTypeAndFederalStateAndYearAndOffice(errorType, federalState, year, office))
                .thenReturn(Optional.empty());

        // when
        errorProtocolService.reportCardOrdersError(office, federalState, year);

        // then
        ArgumentCaptor<ErrorProtocol> captor = ArgumentCaptor.forClass(ErrorProtocol.class);
        verify(errorProtocolRepository).save(captor.capture());
        ErrorProtocol savedProtocol = captor.getValue();

        assertThat(savedProtocol.getErrorType()).isEqualTo(errorType);
        assertThat(savedProtocol.getFederalState()).isEqualTo(federalState);
        assertThat(savedProtocol.getYear()).isEqualTo(year);
        assertThat(savedProtocol.getOffice()).isEqualTo(office);
        assertThat(savedProtocol.getCount()).isEqualTo(1);
    }

    @Test
    void shouldReportCardOrdersErrorAndUpdateExistingEntry() {
        // given
        String office = "Office1";
        String federalState = "State1";
        int year = 2023;
        ErrorType errorType = ErrorType.CARD_ORDER_ERROR;

        ErrorProtocol existingProtocol = new ErrorProtocol();
        existingProtocol.setErrorType(errorType);
        existingProtocol.setFederalState(federalState);
        existingProtocol.setYear(year);
        existingProtocol.setOffice(office);
        existingProtocol.setCount(5);

        when(errorProtocolRepository.findByErrorTypeAndFederalStateAndYearAndOffice(errorType, federalState, year, office))
                .thenReturn(Optional.of(existingProtocol));

        // when
        errorProtocolService.reportCardOrdersError(office, federalState, year);

        // then
        verify(errorProtocolRepository).save(existingProtocol);
        assertThat(existingProtocol.getCount()).isEqualTo(6);
    }



    @Test
    void shouldReportFailureAndCreateNewEntry() {
        // given
        String federalState = "State1";
        String office = "Office1";
        int year = 2023;
        ErrorType errorType = ErrorType.SYSTEM_ERROR;

        when(errorProtocolRepository.findByErrorTypeAndFederalStateAndYearAndOffice(errorType, federalState, year, office))
                .thenReturn(Optional.empty());

        // when
        errorProtocolService.reportSystemError(office, federalState, year);

        // then
        ArgumentCaptor<ErrorProtocol> captor = ArgumentCaptor.forClass(ErrorProtocol.class);
        verify(errorProtocolRepository).save(captor.capture());
        ErrorProtocol savedProtocol = captor.getValue();

        assertThat(savedProtocol.getErrorType()).isEqualTo(errorType);
        assertThat(savedProtocol.getFederalState()).isEqualTo(federalState);
        assertThat(savedProtocol.getYear()).isEqualTo(year);
        assertThat(savedProtocol.getCount()).isEqualTo(1);
    }

    @Test
    void shouldReportFailureAndUpdateExistingEntry() {
        // given
        String federalState = "State1";
        int year = 2023;
        ErrorType errorType = ErrorType.SYSTEM_ERROR;
        String office = "Office1";

        ErrorProtocol existingProtocol = new ErrorProtocol();
        existingProtocol.setErrorType(errorType);
        existingProtocol.setFederalState(federalState);
        existingProtocol.setYear(year);
        existingProtocol.setCount(5);

        when(errorProtocolRepository.findByErrorTypeAndFederalStateAndYearAndOffice(errorType, federalState, year, office))
                .thenReturn(Optional.of(existingProtocol));

        // when
        errorProtocolService.reportSystemError(office, federalState, year);

        // then
        verify(errorProtocolRepository).save(existingProtocol);
        assertThat(existingProtocol.getCount()).isEqualTo(6);
    }

    @Test
    void shouldReportOSErrorAndCreateNewEntry() {
        // given
        String federalState = "State1";
        int year = 2023;
        ErrorType errorType = ErrorType.ONLINE_SERVICE_ERROR;

        when(errorProtocolRepository.findByErrorTypeAndFederalStateAndYear(errorType, federalState, year))
                .thenReturn(Optional.empty());

        // when
        errorProtocolService.reportOSError(federalState, year);

        // then
        ArgumentCaptor<ErrorProtocol> captor = ArgumentCaptor.forClass(ErrorProtocol.class);
        verify(errorProtocolRepository).save(captor.capture());
        ErrorProtocol savedProtocol = captor.getValue();

        assertThat(savedProtocol.getErrorType()).isEqualTo(errorType);
        assertThat(savedProtocol.getFederalState()).isEqualTo(federalState);
        assertThat(savedProtocol.getYear()).isEqualTo(year);
        assertThat(savedProtocol.getCount()).isEqualTo(1);
    }

    @Test
    void shouldReportOSErrorAndUpdateExistingEntry() {
        // given
        String federalState = "State1";
        int year = 2023;
        ErrorType errorType = ErrorType.ONLINE_SERVICE_ERROR;

        ErrorProtocol existingProtocol = new ErrorProtocol();
        existingProtocol.setErrorType(errorType);
        existingProtocol.setFederalState(federalState);
        existingProtocol.setYear(year);
        existingProtocol.setCount(5);

        when(errorProtocolRepository.findByErrorTypeAndFederalStateAndYear(errorType, federalState, year))
                .thenReturn(Optional.of(existingProtocol));

        // when
        errorProtocolService.reportOSError(federalState, year);

        // then
        verify(errorProtocolRepository).save(existingProtocol);
        assertThat(existingProtocol.getCount()).isEqualTo(6);
    }
}