package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ErrorsStatisticsServiceImplTest {

    @Mock
    private ErrorsProtocolStatisticsPort errorsProtocolStatisticsPort;

    private ErrorsStatisticsServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new ErrorsStatisticsServiceImpl(errorsProtocolStatisticsPort);
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getAvailableYears should return years from port")
    void getAvailableYears_ShouldReturnYearsFromPort() {
        // given
        List<Integer> expectedYears = List.of(2023, 2024, 2025);
        when(errorsProtocolStatisticsPort.getAvailableYears()).thenReturn(expectedYears);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertThat(result).isEqualTo(expectedYears);
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getStatisticsByYears should return statistics for all years")
    void getStatisticsByYears_ShouldReturnStatisticsForAllYears() {
        // given
        List<Integer> years = List.of(2023, 2024);

        // Mock port responses for 2023
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2023)).thenReturn(5);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2023)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2023)).thenReturn(2);

        // Mock port responses for 2024 (no data)
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2024)).thenReturn(0);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2024)).thenReturn(0);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2024)).thenReturn(0);

        // when
        List<ErrorsStatistics> result = service.getStatisticsByYears(years);

        // then
        assertThat(result).hasSize(2);

        // Check 2023 data
        ErrorsStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.data().onlineService()).isEqualTo(5);
        assertThat(stats2023.data().cardPrinterService()).isEqualTo(3);
        assertThat(stats2023.data().system()).isEqualTo(2);

        // Check 2024 data (should be zero)
        ErrorsStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.data().onlineService()).isEqualTo(0);
        assertThat(stats2024.data().cardPrinterService()).isEqualTo(0);
        assertThat(stats2024.data().system()).isEqualTo(0);
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getStatisticsByFederalStateAndOfficeAndYears should filter by federal state and office")
    void getStatisticsByFederalStateAndOfficeAndYears_ShouldFilterByFederalStateAndOffice() {
        // given
        List<Integer> years = List.of(2023);
        String federalState = "BY";
        String office = "Office1";

        // Mock port responses for filtered data
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, office)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, office)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, office)).thenReturn(1);

        // when
        List<ErrorsStatistics> result = service.getStatisticsByFederalStateAndOfficeAndYears(federalState, office, years);

        // then
        assertThat(result).hasSize(1);

        ErrorsStatistics stats = result.get(0);
        assertThat(stats.year()).isEqualTo(2023);
        assertThat(stats.data().onlineService()).isEqualTo(2);
        assertThat(stats.data().cardPrinterService()).isEqualTo(3);
        assertThat(stats.data().system()).isEqualTo(1);
    }
}
