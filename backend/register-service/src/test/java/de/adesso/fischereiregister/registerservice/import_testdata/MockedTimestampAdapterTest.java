package de.adesso.fischereiregister.registerservice.import_testdata;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class MockedTimestampAdapterTest {

    private MockedTimestampAdapter timestampAdapter;

    @BeforeEach
    void setUp() {
        timestampAdapter = new MockedTimestampAdapter();
    }

    @Test
    @DisplayName("Should push and pop timestamps for specific register entry IDs")
    void shouldPushAndPopTimestampsForSpecificRegisterEntryIds() {
        // given
        UUID registerEntryId1 = UUID.randomUUID();
        UUID registerEntryId2 = UUID.randomUUID();
        Instant timestamp1 = Instant.now();
        Instant timestamp2 = Instant.now().plusSeconds(60);
        Instant timestamp3 = Instant.now().plusSeconds(120);

        // when
        timestampAdapter.pushMockedTimestamp(registerEntryId1, timestamp1);
        timestampAdapter.pushMockedTimestamp(registerEntryId1, timestamp2);
        timestampAdapter.pushMockedTimestamp(registerEntryId2, timestamp3);

        // then
        assertEquals(timestamp2, timestampAdapter.popMockedTimestamp(registerEntryId1));
        assertEquals(timestamp1, timestampAdapter.popMockedTimestamp(registerEntryId1));
        assertEquals(timestamp3, timestampAdapter.popMockedTimestamp(registerEntryId2));
        assertNull(timestampAdapter.popMockedTimestamp(registerEntryId1));
        assertNull(timestampAdapter.popMockedTimestamp(registerEntryId2));
    }

    @Test
    @DisplayName("Should push and pop timestamps from string for specific register entry IDs")
    void shouldPushAndPopTimestampsFromStringForSpecificRegisterEntryIds() {
        // given
        UUID registerEntryId = UUID.randomUUID();
        String timestampString = "01.01.2023 12:00:00";

        // when
        timestampAdapter.pushMockedTimestamp(registerEntryId, timestampString);
        Instant result = timestampAdapter.popMockedTimestamp(registerEntryId);

        // then
        assertNotNull(result);
        LocalDateTime expectedDateTime = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        Instant expectedInstant = expectedDateTime.atZone(ZoneId.systemDefault()).toInstant();
        assertEquals(expectedInstant, result);
    }

    @Test
    @DisplayName("Should return effective timestamp with mocked timestamp when available")
    void shouldReturnEffectiveTimestampWithMockedTimestampWhenAvailable() {
        // given
        UUID registerEntryId = UUID.randomUUID();
        Instant mockedTimestamp = Instant.now();
        Instant realTimestamp = Instant.now().plusSeconds(60);
        timestampAdapter.pushMockedTimestamp(registerEntryId, mockedTimestamp);

        // when
        Instant result = timestampAdapter.getEffectiveTimestamp(registerEntryId, realTimestamp);

        // then
        assertEquals(mockedTimestamp, result);
    }

    @Test
    @DisplayName("Should return real timestamp when no mocked timestamp available")
    void shouldReturnRealTimestampWhenNoMockedTimestampAvailable() {
        // given
        UUID registerEntryId = UUID.randomUUID();
        Instant realTimestamp = Instant.now();

        // when
        Instant result = timestampAdapter.getEffectiveTimestamp(registerEntryId, realTimestamp);

        // then
        assertEquals(realTimestamp, result);
    }

    @Test
    @DisplayName("Should handle null register entry ID gracefully")
    void shouldHandleNullRegisterEntryIdGracefully() {
        // given
        Instant timestamp = Instant.now();

        // when & then
        assertDoesNotThrow(() -> timestampAdapter.pushMockedTimestamp(null, timestamp));
        assertNull(timestampAdapter.popMockedTimestamp(null));
        assertEquals(timestamp, timestampAdapter.getEffectiveTimestamp(null, timestamp));
    }

    @Test
    @DisplayName("Should clear all timestamps including UUID-based ones")
    void shouldClearAllTimestampsIncludingUuidBasedOnes() {
        // given
        UUID registerEntryId = UUID.randomUUID();
        Instant timestamp = Instant.now();
        timestampAdapter.pushMockedTimestamp(registerEntryId, timestamp);
        timestampAdapter.pushMockedTimestamp(timestamp); // legacy method

        // when
        timestampAdapter.clearMockedTimestamps();

        // then
        assertNull(timestampAdapter.popMockedTimestamp(registerEntryId));
        assertNull(timestampAdapter.popMockedTimestamp()); // legacy method
    }

    @Test
    @DisplayName("Should maintain separate stacks for different register entry IDs")
    void shouldMaintainSeparateStacksForDifferentRegisterEntryIds() {
        // given
        UUID registerEntryId1 = UUID.randomUUID();
        UUID registerEntryId2 = UUID.randomUUID();
        Instant timestamp1 = Instant.now();
        Instant timestamp2 = Instant.now().plusSeconds(60);

        // when
        timestampAdapter.pushMockedTimestamp(registerEntryId1, timestamp1);
        timestampAdapter.pushMockedTimestamp(registerEntryId2, timestamp2);

        // then
        assertEquals(timestamp1, timestampAdapter.popMockedTimestamp(registerEntryId1));
        assertEquals(timestamp2, timestampAdapter.popMockedTimestamp(registerEntryId2));
        assertNull(timestampAdapter.popMockedTimestamp(registerEntryId1));
        assertNull(timestampAdapter.popMockedTimestamp(registerEntryId2));
    }

    @Test
    @DisplayName("Should maintain backward compatibility with legacy methods")
    void shouldMaintainBackwardCompatibilityWithLegacyMethods() {
        // given
        Instant timestamp = Instant.now();
        Instant realTimestamp = Instant.now().plusSeconds(60);

        // when
        timestampAdapter.pushMockedTimestamp(timestamp);
        Instant result = timestampAdapter.getEffectiveTimestamp(realTimestamp);

        // then
        assertEquals(timestamp, result);
    }
}
