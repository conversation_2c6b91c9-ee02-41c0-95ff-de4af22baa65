package de.adesso.fischereiregister.registerservice.domain.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface ErrorsStatisticsDataMapper {

    ErrorsStatisticsDataMapper INSTANCE = Mappers.getMapper(ErrorsStatisticsDataMapper.class);

    org.openapitools.model.ErrorsStatisticsData toResponse(de.adesso.fischereiregister.core.ports.contracts.statistics.ErrorsStatisticsData list);
}
