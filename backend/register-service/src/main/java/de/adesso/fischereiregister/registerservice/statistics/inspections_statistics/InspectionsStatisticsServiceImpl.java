package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class InspectionsStatisticsServiceImpl implements InspectionsStatisticsService {

    private final InspectorProtocolService inspectorProtocolService;

    @Override
    public List<InspectionsStatistics> getInspectionsStatistics(
            List<Integer> years,
            String federalState) {

        List<InspectionsStatisticsResult> inspectionsStatisticsResults;

        // If years list is empty, get all available years
        final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : inspectorProtocolService.getAvailableYears();

        if (federalState != null && !federalState.isEmpty()) {
            // If federalState is provided
            log.info("Fetching inspectionsStatisticsResults with federalState: {} and years: {}", federalState, yearsToQuery);
            inspectionsStatisticsResults = inspectorProtocolService.getInspectionsStatistics(yearsToQuery, federalState);
        } else {
            // If no federalState specified
            log.info("Fetching inspectionsStatisticsResults for all regions and years: {}", yearsToQuery);
            inspectionsStatisticsResults = inspectorProtocolService.getInspectionsStatistics(yearsToQuery, null);
        }

        // Log the number of statistics results fetched
        log.info("Fetched {} InspectionsStatisticsResults", inspectionsStatisticsResults.size());

        // Transform and return the data
        return transformToInspectionsStatistics(inspectionsStatisticsResults, yearsToQuery);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return inspectorProtocolService.getAvailableYears();
    }

    /**
     * Transforms InspectionsStatisticsResult objects into InspectionsStatistics objects.
     * This method incorporates the logic from the former InspectionsStatisticsTransformationService.
     */
    private List<InspectionsStatistics> transformToInspectionsStatistics(List<InspectionsStatisticsResult> statisticsResults, List<Integer> yearsToQuery) {
        try {
            // Group statistics by year
            Map<Integer, InspectionsStatisticsResult> statsByYear = statisticsResults.stream()
                    .collect(Collectors.toMap(InspectionsStatisticsResult::getYear, result -> result));

            List<InspectionsStatistics> result = new ArrayList<>();

            // For each requested year, create an InspectionsStatistics object
            for (Integer year : yearsToQuery) {
                InspectionsStatisticsResult yearStats = statsByYear.get(year);

                int activeInspectors = 0;
                int numberOfInspections = 0;

                if (yearStats != null && yearStats.getData() != null) {
                    activeInspectors = yearStats.getData().getActiveInspectors() != null ? yearStats.getData().getActiveInspectors() : 0;
                    numberOfInspections = yearStats.getData().getNumberOfInspections() != null ? yearStats.getData().getNumberOfInspections() : 0;
                }

                // Create data entry with active inspectors and number of inspections (zero if no data)
                InspectionsStatisticsData data = new InspectionsStatisticsData(activeInspectors, numberOfInspections);

                // Create InspectionsStatistics with the year and data entry
                InspectionsStatistics inspectionsStatistics = new InspectionsStatistics(year, data);
                result.add(inspectionsStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(InspectionsStatistics::year).reversed());

            return result;
        } catch (Exception e) {
            log.error("Error transforming inspections statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform inspections statistics", e);
        }
    }
}
