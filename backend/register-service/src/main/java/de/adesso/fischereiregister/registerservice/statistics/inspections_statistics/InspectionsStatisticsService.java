package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import java.util.List;

/**
 * Service for managing inspections statistics operations.
 * This service handles data retrieval, transformation, and provides a unified interface
 * for inspections statistics without exposing protocol services or transformation services.
 */
public interface InspectionsStatisticsService {

    /**
     * Retrieves inspections statistics with optional filtering.
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param federalState The federal state to filter by (optional).
     * @return A list of transformed inspections statistics domain objects.
     */
    List<InspectionsStatistics> getInspectionsStatistics(
            List<Integer> years,
            String federalState
    );

    /**
     * Retrieves all available years for which inspections statistics data exists.
     *
     * @return A list of years for which statistics are available.
     */
    List<Integer> getAvailableYears();
}
