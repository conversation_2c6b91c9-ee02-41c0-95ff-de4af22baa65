package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import java.util.List;

public interface ErrorsStatisticsService {

    /**
     * gets the errors statistics for a given federal state and years
     *
     * @param federalState The federal state for which the statistics are retrieved.
     * @param office       The office for which the statistics are retrieved (optional).
     * @param years        The list of years for which the statistics are requested.
     * @return A list of {@link ErrorsStatistics} objects containing the requested statistics.
     */
    List<ErrorsStatistics> getStatisticsByFederalStateAndOfficeAndYears(String federalState, String office, List<Integer> years);

    /**
     * gets the errors statistics for a given years
     *
     * @param years The list of years for which the statistics are requested.
     * @return A list of {@link ErrorsStatistics} objects containing the requested statistics.
     */
    List<ErrorsStatistics> getStatisticsByYears(List<Integer> years);

    /**
     * gets all available years for which error statistics exist
     *
     * @return A list of available years.
     */
    List<Integer> getAvailableYears();
}
