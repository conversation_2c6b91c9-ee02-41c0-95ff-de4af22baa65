package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;

import java.util.List;

public interface ErrorsStatisticsExportService {

    /**
     * Exports the errors statistics for a given federal state and years
     *
     * @param federalState The federal state for which the statistics are retrieved.
     * @param office       The office for which the statistics are retrieved (optional).
     * @param years        The list of years for which the statistics are requested.
     * @param exportType   The export type (e.g., CSV).
     * @return A RenderedContent object containing the exported data.
     */
    RenderedContent exportStatisticsByFederalStateAndOfficeAndYears(String federalState, String office, List<Integer> years, StatisticsExportType exportType);

    /**
     * Exports the errors statistics for a given years
     *
     * @param years      The list of years for which the statistics are requested.
     * @param exportType The export type (e.g., CSV).
     * @return A RenderedContent object containing the exported data.
     */
    RenderedContent exportStatisticsByYears(List<Integer> years, StatisticsExportType exportType);
}
