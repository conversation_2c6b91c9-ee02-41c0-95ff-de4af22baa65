package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import java.util.List;

/**
 * Service for managing certifications statistics operations.
 * This service handles data retrieval, transformation, and provides a unified interface
 * for certifications statistics without exposing view services or transformation services.
 */
public interface CertificationsStatisticsService {

    /**
     * Retrieves certifications statistics with optional filtering.
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office (issuer) to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @return A list of transformed certifications statistics domain objects.
     */
    List<CertificationsStatistics> getCertificationsStatistics(
            List<Integer> years,
            String office,
            String federalState
    );

    /**
     * Retrieves all available years for which certifications statistics data exists.
     *
     * @return A list of years for which statistics are available.
     */
    List<Integer> getAvailableYears();
}
