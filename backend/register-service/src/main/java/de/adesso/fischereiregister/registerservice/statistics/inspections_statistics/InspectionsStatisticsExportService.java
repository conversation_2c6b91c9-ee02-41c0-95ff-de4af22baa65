package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;

import java.util.List;

public interface InspectionsStatisticsExportService {

    /**
     * Exports inspections statistics with optional filtering.
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param federalState The federal state to filter by (optional).
     * @param exportType   The export type (e.g., CSV).
     * @return A RenderedContent object containing the exported data.
     */
    RenderedContent exportInspectionsStatistics(
            List<Integer> years,
            String federalState,
            StatisticsExportType exportType
    );
}
