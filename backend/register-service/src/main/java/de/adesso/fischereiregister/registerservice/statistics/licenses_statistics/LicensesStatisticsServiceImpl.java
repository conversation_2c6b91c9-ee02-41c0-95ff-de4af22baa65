package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class LicensesStatisticsServiceImpl implements LicensesStatisticsService {

    private final LicensesStatisticsViewService licensesStatisticsViewService;

    @Override
    public List<LicensesStatistics> getLicensesStatistics(
            LicenseType licenseType,
            List<Integer> years,
            String office,
            String federalState) {

        List<LicensesStatisticsView> licensesStatisticsViews;

        // If years list is empty, get all available years
        final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : licensesStatisticsViewService.getAvailableYears();

        if (office != null && !office.isEmpty()) {
            // Office has priority over federalState
            log.info("Fetching licensesStatisticsViews for licenseType: {} with office: {} and years: {}", licenseType, office, yearsToQuery);
            licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndOfficeAndYears(licenseType, office, yearsToQuery);
        } else if (federalState != null && !federalState.isEmpty()) {
            // If no office specified but federalState is provided
            log.info("Fetching licensesStatisticsViews for licenseType: {} with federalState: {} and years: {}", licenseType, federalState, yearsToQuery);
            licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, yearsToQuery);
        } else {
            // If neither office nor federalState specified
            log.info("Fetching licensesStatisticsViews for licenseType: {} for all regions and years: {}", licenseType, yearsToQuery);
            licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, yearsToQuery);
        }

        // Log the number of statistics views fetched
        log.info("Fetched {} LicensesStatisticsViews for licenseType: {}", licensesStatisticsViews.size(), licenseType);

        // Transform and return the data
        return transformToLicensesStatistics(licensesStatisticsViews, yearsToQuery);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return licensesStatisticsViewService.getAvailableYears();
    }

    /**
     * Transforms LicensesStatisticsView objects into LicensesStatistics objects.
     * This method incorporates the logic from the former LicensesStatisticsTransformationService.
     */
    private List<LicensesStatistics> transformToLicensesStatistics(List<LicensesStatisticsView> statisticsViews, List<Integer> yearsToQuery) {
        try {
            // Group statistics by year
            Map<Integer, List<LicensesStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(LicensesStatisticsView::getYear));

            List<LicensesStatistics> result = new ArrayList<>();

            // For each requested year, create a LicensesStatistics object
            for (Integer year : yearsToQuery) {
                List<LicensesStatisticsView> yearStats = statsByYear.getOrDefault(year, new ArrayList<>());

                // Group by source (SubmissionType)
                Map<SubmissionType, Integer> countBySubmissionType = yearStats.stream()
                        .collect(Collectors.groupingBy(
                                LicensesStatisticsView::getSource,
                                Collectors.summingInt(LicensesStatisticsView::getCount)
                        ));

                List<LicensesStatisticsDataEntry> data = new ArrayList<>();

                // Ensure every SubmissionType is included
                for (SubmissionType submissionType : SubmissionType.values()) {
                    int count = countBySubmissionType.getOrDefault(submissionType, 0);
                    data.add(new LicensesStatisticsDataEntry(submissionType, count));
                }

                LicensesStatistics licensesStatistics = new LicensesStatistics(year, data);
                result.add(licensesStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(LicensesStatistics::year).reversed());

            return result;

        } catch (Exception e) {
            log.error("Error transforming Licenses statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform licenses statistics", e);
        }
    }
}
