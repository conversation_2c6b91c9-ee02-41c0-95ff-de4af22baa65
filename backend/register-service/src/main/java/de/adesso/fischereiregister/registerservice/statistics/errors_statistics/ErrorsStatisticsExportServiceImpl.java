package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class ErrorsStatisticsExportServiceImpl implements ErrorsStatisticsExportService {

    private final ErrorsProtocolStatisticsPort errorsProtocolStatisticsPort;

    @Override
    public RenderedContent exportStatisticsByFederalStateAndOfficeAndYears(String federalState, String office, List<Integer> years, StatisticsExportType exportType) {
        try {
            log.debug("Exporting errors statistics for federalState: {}, office: {}, years: {} with export type: {}",
                    federalState, office, years, exportType);

            List<ErrorsStatistics> errorsStatistics = new ArrayList<>();

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : errorsProtocolStatisticsPort.getAvailableYears();

            for (Integer year : yearsToQuery) {
                int onlineServiceErrors = errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);
                int cardErrors = errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);
                int systemErrors = errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);

                ErrorsStatisticsData data = new ErrorsStatisticsData(onlineServiceErrors, cardErrors, systemErrors);
                ErrorsStatistics errorsStatistic = new ErrorsStatistics(year, data);
                errorsStatistics.add(errorsStatistic);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateErrorsStatisticsCsv(errorsStatistics, federalState, office);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting errors statistics for federalState {}, office {}, years {}: {}",
                    federalState, office, years, e.getMessage(), e);
            throw new RuntimeException("Failed to export errors statistics", e);
        }
    }

    @Override
    public RenderedContent exportStatisticsByYears(List<Integer> years, StatisticsExportType exportType) {
        try {
            log.debug("Exporting errors statistics for years: {} with export type: {}", years, exportType);

            List<ErrorsStatistics> errorsStatistics = new ArrayList<>();

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : errorsProtocolStatisticsPort.getAvailableYears();

            for (Integer year : yearsToQuery) {
                int onlineServiceErrors = errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(year);
                int cardErrors = errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(year);
                int systemErrors = errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(year);

                ErrorsStatisticsData data = new ErrorsStatisticsData(onlineServiceErrors, cardErrors, systemErrors);
                ErrorsStatistics errorsStatistic = new ErrorsStatistics(year, data);
                errorsStatistics.add(errorsStatistic);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateErrorsStatisticsCsv(errorsStatistics, null, null);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting errors statistics for years {}: {}", years, e.getMessage(), e);
            throw new RuntimeException("Failed to export errors statistics for years " + years, e);
        }
    }

    private RenderedContent generateErrorsStatisticsCsv(
            List<ErrorsStatistics> errorsStatistics, 
            String federalState, 
            String office) throws IOException {
        
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Year", "Online Service Errors", "Card Errors", "System Errors")
                .build();

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            for (ErrorsStatistics statistic : errorsStatistics) {
                csvPrinter.printRecord(
                        statistic.year(),
                        statistic.data().onlineService(),
                        statistic.data().cardPrinterService(),
                        statistic.data().system()
                );
            }
        }

        String filename = buildFilename(federalState, office);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new RenderedContent(filename, RenderedContentType.CSV, content);
    }

    private String buildFilename(String federalState, String office) {
        StringBuilder filename = new StringBuilder("errors-statistics");
        
        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }
        
        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "_"));
        }
        
        filename.append("-").append(getCurrentTimestamp());
        
        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
