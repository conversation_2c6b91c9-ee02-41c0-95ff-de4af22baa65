package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class TaxesStatisticsServiceImpl implements TaxesStatisticsService {

    private final TaxesStatisticsViewService taxesStatisticsViewService;

    @Override
    public List<TaxesStatistics> getTaxesStatistics(
            List<Integer> years,
            String office,
            String federalState) {

        List<TaxesStatisticsView> taxesStatisticsViews;

        // If years list is empty, get all available years
        final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : taxesStatisticsViewService.getAvailableYears();

        if (office != null && !office.isEmpty()) {
            // Office has priority over federalState
            log.info("Fetching taxesStatisticsViews with office: {} and years: {}", office, yearsToQuery);
            taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByOfficeAndYears(office, yearsToQuery);
        } else if (federalState != null && !federalState.isEmpty()) {
            // If no office specified but federalState is provided
            log.info("Fetching taxesStatisticsViews with federalState: {} and years: {}", federalState, yearsToQuery);
            taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, yearsToQuery);
        } else {
            // If neither office nor federalState specified
            log.info("Fetching taxesStatisticsViews for all regions and years: {}", yearsToQuery);
            taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByYears(yearsToQuery);
        }

        // Log the number of statistics views fetched
        log.info("Fetched {} TaxesStatisticsViews", taxesStatisticsViews.size());

        // Transform and return the data
        return transformToTaxesStatistics(taxesStatisticsViews, yearsToQuery);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return taxesStatisticsViewService.getAvailableYears();
    }

    /**
     * Transforms TaxesStatisticsView objects into TaxesStatistics objects.
     * This method incorporates the logic from the former TaxesStatisticsTransformationService.
     */
    private List<TaxesStatistics> transformToTaxesStatistics(List<TaxesStatisticsView> statisticsViews, List<Integer> yearsToQuery) {
        try {
            // Group statistics by year
            Map<Integer, List<TaxesStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(TaxesStatisticsView::getYear));

            List<TaxesStatistics> result = new ArrayList<>();

            // For each requested year, create a TaxesStatistics object
            for (Integer year : yearsToQuery) {
                List<TaxesStatisticsView> yearStats = statsByYear.get(year);
                List<TaxesStatisticsDataEntry> data = new ArrayList<>();

                if (yearStats != null && !yearStats.isEmpty()) {
                    // Group by source and duration
                    Map<String, List<TaxesStatisticsView>> groupedBySourceAndDuration = yearStats.stream()
                            .collect(Collectors.groupingBy(view -> view.getSource() + "_" + view.getDuration()));

                    for (Map.Entry<String, List<TaxesStatisticsView>> entry : groupedBySourceAndDuration.entrySet()) {
                        List<TaxesStatisticsView> views = entry.getValue();
                        if (!views.isEmpty()) {
                            TaxesStatisticsView firstView = views.getFirst();
                            SubmissionType source = firstView.getSource();
                            int duration = firstView.getDuration();

                            int totalCount = views.stream().mapToInt(TaxesStatisticsView::getCount).sum();
                            double totalRevenue = views.stream().mapToDouble(TaxesStatisticsView::getRevenue).sum();

                            // Add entry for this submission type and duration
                            TaxesStatisticsDataEntry dataItem = new TaxesStatisticsDataEntry(source, duration, totalCount, totalRevenue);
                            data.add(dataItem);

                            // Add zero entry for the other submission type to ensure both are included
                            SubmissionType remainingSource = source == SubmissionType.ONLINE ? SubmissionType.ANALOG : SubmissionType.ONLINE;
                            TaxesStatisticsDataEntry remainingDataItem = new TaxesStatisticsDataEntry(remainingSource, duration, 0, 0.0);
                            data.add(remainingDataItem);
                        }
                    }
                } else {
                    // Create zero entries for both submission types and common durations
                    data.add(new TaxesStatisticsDataEntry(SubmissionType.ONLINE, 1, 0, 0.0));
                    data.add(new TaxesStatisticsDataEntry(SubmissionType.ANALOG, 1, 0, 0.0));
                }

                TaxesStatistics taxesStatistics = new TaxesStatistics(year, data);
                result.add(taxesStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(TaxesStatistics::year).reversed());

            return result;

        } catch (Exception e) {
            log.error("Error transforming Taxes statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform taxes statistics", e);
        }
    }
}
