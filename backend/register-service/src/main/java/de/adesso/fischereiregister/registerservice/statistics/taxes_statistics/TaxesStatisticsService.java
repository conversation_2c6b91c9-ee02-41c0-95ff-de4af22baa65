package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import java.util.List;

/**
 * Service for managing taxes statistics operations.
 * This service handles data retrieval, transformation, and provides a unified interface
 * for taxes statistics without exposing view services or transformation services.
 */
public interface TaxesStatisticsService {

    /**
     * Retrieves taxes statistics with optional filtering.
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @return A list of transformed taxes statistics domain objects.
     */
    List<TaxesStatistics> getTaxesStatistics(
            List<Integer> years,
            String office,
            String federalState
    );

    /**
     * Retrieves all available years for which taxes statistics data exists.
     *
     * @return A list of years for which statistics are available.
     */
    List<Integer> getAvailableYears();
}
