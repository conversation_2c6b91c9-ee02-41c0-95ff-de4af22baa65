package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class CertificationsStatisticsExportServiceImpl implements CertificationsStatisticsExportService {

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Override
    public RenderedContent exportCertificationsStatistics(
            List<Integer> years,
            String office,
            String federalState,
            StatisticsExportType exportType) {
        try {
            log.debug("Exporting certifications statistics for years: {}, office: {}, federalState: {} with export type: {}",
                    years, office, federalState, exportType);

            List<CertificationsStatisticsView> certificationsStatisticsViews;

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : certificationsStatisticsViewService.getAvailableYears();

            // if federalState is provided the office filter is ignored
            if (federalState != null && !federalState.isEmpty()) {
                certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, yearsToQuery);
            } else if (office != null && !office.isEmpty()) {
                certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByIssuerAndYears(office, yearsToQuery);
            } else {
                certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByYears(yearsToQuery);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateCertificationsStatisticsCsv(certificationsStatisticsViews, office, federalState);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting certifications statistics for years {}, office {}, federalState {}: {}",
                    years, office, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export certifications statistics", e);
        }
    }

    private RenderedContent generateCertificationsStatisticsCsv(
            List<CertificationsStatisticsView> certificationsStatisticsViews,
            String office,
            String federalState) throws IOException {

        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Bundesland", "Jahr", "Prüfungsinstitution", "Anzahl")
                .build();

        // Group and aggregate the data by federalState, year, issuer
        Map<String, Integer> aggregatedData = certificationsStatisticsViews.stream()
                .collect(Collectors.groupingBy(
                        view -> view.getFederalState() + "|" + view.getYear() + "|" + view.getIssuer(),
                        Collectors.summingInt(CertificationsStatisticsView::getCount)
                ));

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            // Sort by key to ensure consistent ordering
            aggregatedData.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        try {
                            String[] keyParts = entry.getKey().split("\\|");
                            csvPrinter.printRecord(
                                    keyParts[0], // federalState
                                    keyParts[1], // year
                                    keyParts[2], // issuer
                                    entry.getValue() // aggregated count
                            );
                        } catch (IOException e) {
                            throw new RuntimeException("Error writing CSV record", e);
                        }
                    });
        }

        String filename = buildFilename(office, federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new RenderedContent(filename, RenderedContentType.CSV, content);
    }

    private String buildFilename(String office, String federalState) {
        StringBuilder filename = new StringBuilder("certifications-statistics");

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }

        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "_"));
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
