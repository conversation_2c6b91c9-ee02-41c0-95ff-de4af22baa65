package de.adesso.fischereiregister.registerservice.statistics.metadata;

import java.util.List;

/**
 * Service for retrieving metadata from various statistics views.
 * This service aggregates data from multiple statistics sources to provide unified metadata endpoints.
 */
public interface StatisticsMetadataService {

    /**
     * Retrieves all available certification issuers without any filtering.
     *
     * @return A list of distinct issuer names sorted alphabetically.
     */
    List<String> getAvailableCertificationIssuers();

    /**
     * Retrieves available certification issuers filtered by years.
     *
     * @param years The years to filter by.
     * @return A list of distinct issuer names sorted alphabetically.
     */
    List<String> getAvailableCertificationIssuersByYears(List<Integer> years);

    /**
     * Retrieves available certification issuers filtered by federal state.
     *
     * @param federalState The federal state to filter by.
     * @return A list of distinct issuer names sorted alphabetically.
     */
    List<String> getAvailableCertificationIssuersByFederalState(String federalState);

    /**
     * Retrieves available certification issuers filtered by both years and federal state.
     *
     * @param years        The years to filter by.
     * @param federalState The federal state to filter by.
     * @return A list of distinct issuer names sorted alphabetically.
     */
    List<String> getAvailableCertificationIssuersByYearsAndFederalState(List<Integer> years, String federalState);

    /**
     * Retrieves all available offices from both taxes and licenses statistics without any filtering.
     * This method aggregates offices from multiple statistics sources.
     *
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOffices();

    /**
     * Retrieves available offices from both taxes and licenses statistics filtered by years.
     * This method aggregates offices from multiple statistics sources.
     *
     * @param years The years to filter by.
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOfficesByYears(List<Integer> years);

    /**
     * Retrieves available offices from both taxes and licenses statistics filtered by federal state.
     * This method aggregates offices from multiple statistics sources.
     *
     * @param federalState The federal state to filter by.
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOfficesByFederalState(String federalState);

    /**
     * Retrieves available offices from both taxes and licenses statistics filtered by both years and federal state.
     * This method aggregates offices from multiple statistics sources.
     *
     * @param years        The years to filter by.
     * @param federalState The federal state to filter by.
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOfficesByYearsAndFederalState(List<Integer> years, String federalState);

    /**
     * Retrieves all available years from all statistics sources.
     * This method aggregates years from certifications, taxes, licenses, bans, fees, and inspections statistics.
     *
     * @return A list of distinct years sorted in descending order.
     */
    List<Integer> getAvailableYears();
}
