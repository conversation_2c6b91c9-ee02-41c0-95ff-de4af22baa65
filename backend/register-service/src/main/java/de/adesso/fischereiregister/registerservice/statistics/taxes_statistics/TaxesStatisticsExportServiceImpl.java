package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class TaxesStatisticsExportServiceImpl implements TaxesStatisticsExportService {

    private final TaxesStatisticsViewService taxesStatisticsViewService;

    @Override
    public RenderedContent exportTaxesStatistics(
            List<Integer> years,
            String office,
            String federalState,
            StatisticsExportType exportType) {
        try {
            log.debug("Exporting taxes statistics for years: {}, office: {}, federalState: {} with export type: {}",
                    years, office, federalState, exportType);

            List<TaxesStatisticsView> taxesStatisticsViews;

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : taxesStatisticsViewService.getAvailableYears();

            // if federalState is provided the office filter is ignored
            if (federalState != null && !federalState.isEmpty()) {
                taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, yearsToQuery);
            } else if (office != null && !office.isEmpty()) {
                taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByOfficeAndYears(office, yearsToQuery);
            } else {
                taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByYears(yearsToQuery);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateTaxesStatisticsCsv(taxesStatisticsViews, office, federalState);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting taxes statistics for years {}, office {}, federalState {}: {}",
                    years, office, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export taxes statistics", e);
        }
    }

    private RenderedContent generateTaxesStatisticsCsv(
            List<TaxesStatisticsView> taxesStatisticsViews,
            String office,
            String federalState) throws IOException {

        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Bundesland", "Jahr", "Behörde", "Antragsweg", "Gültigkeitszeitraum", "Anzahl", "Betrag")
                .build();

        // Group and aggregate the data by federalState, year, office, source, duration
        Map<String, AggregatedTaxData> aggregatedData = taxesStatisticsViews.stream()
                .collect(Collectors.groupingBy(
                        view -> view.getFederalState() + "|" + view.getYear() + "|" + view.getOffice() + "|" + view.getSource() + "|" + view.getDuration(),
                        Collectors.reducing(
                                new AggregatedTaxData(0, 0.0),
                                view -> new AggregatedTaxData(view.getCount(), view.getRevenue()),
                                (a, b) -> new AggregatedTaxData(a.count + b.count, a.revenue + b.revenue)
                        )
                ));

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            // Sort by key to ensure consistent ordering
            aggregatedData.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        try {
                            String[] keyParts = entry.getKey().split("\\|");
                            AggregatedTaxData data = entry.getValue();
                            csvPrinter.printRecord(
                                    keyParts[0], // federalState
                                    keyParts[1], // year
                                    keyParts[2], // office
                                    keyParts[3], // source
                                    keyParts[4], // duration
                                    data.count,  // aggregated count
                                    data.revenue // aggregated revenue
                            );
                        } catch (IOException e) {
                            throw new RuntimeException("Error writing CSV record", e);
                        }
                    });
        }

        String filename = buildFilename(office, federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new RenderedContent(filename, RenderedContentType.CSV, content);
    }

    private static class AggregatedTaxData {
        final int count;
        final double revenue;

        AggregatedTaxData(int count, double revenue) {
            this.count = count;
            this.revenue = revenue;
        }
    }

    private String buildFilename(String office, String federalState) {
        StringBuilder filename = new StringBuilder("taxes-statistics");
        
        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }
        
        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "_"));
        }
        
        filename.append("-").append(getCurrentTimestamp());
        
        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
