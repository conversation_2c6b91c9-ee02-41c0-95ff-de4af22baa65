package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class CertificationsStatisticsServiceImpl implements CertificationsStatisticsService {

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Override
    public List<CertificationsStatistics> getCertificationsStatistics(
            List<Integer> years,
            String office,
            String federalState) {

        List<CertificationsStatisticsView> certificationsStatisticsViews;

        // If years list is empty, get all available years
        final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : certificationsStatisticsViewService.getAvailableYears();

        if (office != null && !office.isEmpty()) {
            // Office has priority over federalState
            log.info("Fetching certificationsStatisticsViews with issuer: {} and years: {}", office, yearsToQuery);
            certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByIssuerAndYears(office, yearsToQuery);
        } else if (federalState != null && !federalState.isEmpty()) {
            // If no office specified but federalState is provided
            log.info("Fetching certificationsStatisticsViews with federalState: {} and years: {}", federalState, yearsToQuery);
            certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, yearsToQuery);
        } else {
            // If neither office nor federalState specified
            log.info("Fetching certificationsStatisticsViews for all regions and years: {}", yearsToQuery);
            certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByYears(yearsToQuery);
        }

        // Log the number of statistics views fetched
        log.info("Fetched {} CertificationsStatisticsViews", certificationsStatisticsViews.size());

        // Transform and return the data
        return transformToCertificationsStatistics(certificationsStatisticsViews, yearsToQuery);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return certificationsStatisticsViewService.getAvailableYears();
    }
    /**
     * Transforms CertificationsStatisticsView objects into CertificationsStatistics objects.
     * This method incorporates the logic from the former CertificationsStatisticsTransformationService.
     */
    private List<CertificationsStatistics> transformToCertificationsStatistics(List<CertificationsStatisticsView> statisticsViews, List<Integer> yearsToQuery) {
        try {
            // Extract all unique issuers from the provided views
            Set<String> allIssuers = statisticsViews.stream()
                    .map(CertificationsStatisticsView::getIssuer)
                    .collect(Collectors.toSet());

            // Group statistics by year
            Map<Integer, List<CertificationsStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(CertificationsStatisticsView::getYear));

            List<CertificationsStatistics> result = new ArrayList<>();

            // For each requested year, create a CertificationsStatistics object
            for (Integer year : yearsToQuery) {
                List<CertificationsStatisticsView> yearStats = statsByYear.getOrDefault(year, new ArrayList<>());

                // Group by issuer for this year
                Map<String, Integer> countByIssuer = yearStats.stream()
                        .collect(Collectors.groupingBy(
                                CertificationsStatisticsView::getIssuer,
                                Collectors.summingInt(CertificationsStatisticsView::getCount)
                        ));

                List<CertificationsStatisticsData> data = new ArrayList<>();

                // Ensure every issuer is included (with zero if no data for this year)
                for (String issuer : allIssuers) {
                    int amount = countByIssuer.getOrDefault(issuer, 0);
                    data.add(new CertificationsStatisticsData(issuer, amount));
                }

                // Sort data by issuer name for consistent ordering
                data.sort(Comparator.comparing(CertificationsStatisticsData::issuer));

                CertificationsStatistics certificationsStatistics = new CertificationsStatistics(year, data);
                result.add(certificationsStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(CertificationsStatistics::year).reversed());

            return result;

        } catch (Exception e) {
            log.error("Error transforming Certifications statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform certifications statistics", e);
        }
    }
}
