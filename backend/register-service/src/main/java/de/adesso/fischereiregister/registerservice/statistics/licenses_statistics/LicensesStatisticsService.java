package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;

import java.util.List;

/**
 * Service for managing licenses statistics operations.
 * This service handles data retrieval, transformation, and provides a unified interface
 * for licenses statistics without exposing view services or transformation services.
 */
public interface LicensesStatisticsService {

    /**
     * Retrieves licenses statistics for a specific license type with optional filtering.
     *
     * @param licenseType  The type of license (e.g., REGULAR, LIMITED, VACATION).
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @return A list of transformed licenses statistics domain objects.
     */
    List<LicensesStatistics> getLicensesStatistics(
            LicenseType licenseType,
            List<Integer> years,
            String office,
            String federalState
    );

    /**
     * Retrieves all available years for which licenses statistics data exists.
     *
     * @return A list of years for which statistics are available.
     */
    List<Integer> getAvailableYears();
}
