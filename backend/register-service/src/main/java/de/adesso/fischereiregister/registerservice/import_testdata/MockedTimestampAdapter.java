package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.core.ports.TimestampPort;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayDeque;
import java.util.Deque;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class MockedTimestampAdapter implements TimestampPort {

    private static final DateTimeFormatter GERMAN_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");

    private final ThreadLocal<Map<UUID, Deque<Instant>>> mockedTimestampsByRegisterEntry = ThreadLocal.withInitial(HashMap::new);

    @Override
    public void pushMockedTimestamp(UUID registerEntryId, Instant timestamp) {
        if (registerEntryId != null && timestamp != null) {
            Map<UUID, Deque<Instant>> timestampMap = mockedTimestampsByRegisterEntry.get();
            timestampMap.computeIfAbsent(registerEntryId, k -> new ArrayDeque<>()).push(timestamp);
        }
    }

    @Override
    public void pushMockedTimestamp(UUID registerEntryId, String timestampString) {
        if (registerEntryId == null || timestampString == null || timestampString.isBlank()) {
            return;
        }
        try {
            pushMockedTimestamp(registerEntryId, parseTimestamp(timestampString.trim()));
        } catch (DateTimeParseException ignored) {
            // Intentionally ignored for malformed inputs
        }
    }

    @Override
    public Instant popMockedTimestamp(UUID registerEntryId) {
        if (registerEntryId == null) {
            return null;
        }
        Map<UUID, Deque<Instant>> timestampMap = mockedTimestampsByRegisterEntry.get();
        Deque<Instant> stack = timestampMap.get(registerEntryId);
        if (stack == null || stack.isEmpty()) {
            return null;
        }
        Instant timestamp = stack.pop();
        // Clean up empty stacks to prevent memory leaks
        if (stack.isEmpty()) {
            timestampMap.remove(registerEntryId);
        }
        return timestamp;
    }

    @Override
    public void clearMockedTimestamps() {
        mockedTimestampsByRegisterEntry.get().clear();
    }

    @Override
    public Instant getEffectiveTimestamp(UUID registerEntryId, Instant realTimestamp) {
        Instant mocked = popMockedTimestamp(registerEntryId);
        return mocked != null ? mocked : realTimestamp;
    }

    private Instant parseTimestamp(String input) {
        try {
            return LocalDateTime.parse(input, GERMAN_DATE_TIME_FORMATTER)
                    .atZone(ZoneId.systemDefault()).toInstant();
        } catch (DateTimeParseException e) {
            // Try with just the date (defaulting time to noon)
            LocalDateTime date = LocalDateTime.parse(input + " 12:00:00", GERMAN_DATE_TIME_FORMATTER);
            return date.atZone(ZoneId.systemDefault()).toInstant();
        }
    }
}
