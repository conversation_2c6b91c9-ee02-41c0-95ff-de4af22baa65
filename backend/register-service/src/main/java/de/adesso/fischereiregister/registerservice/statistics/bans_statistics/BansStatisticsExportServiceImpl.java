package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class BansStatisticsExportServiceImpl implements BansStatisticsExportService {

    private final BanViewService banViewService;

    @Override
    public RenderedContent exportStatisticsByFederalStateAndYears(String federalState, List<Integer> years, StatisticsExportType exportType) {
        try {
            log.debug("Exporting bans statistics for federal state: {} and years: {} with export type: {}", federalState, years, exportType);

            List<BansStatistics> bansStatistics = new ArrayList<>();
            for (Integer year : years) {
                Integer issuedCount = banViewService.getIssuedAmountByFederalStateAndYear(federalState, year);
                BansStatisticsData data = new BansStatisticsData(issuedCount);
                BansStatistics bansStatistic = new BansStatistics(year, data);
                bansStatistics.add(bansStatistic);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateBansStatisticsCsv(bansStatistics, "bans-statistics-" + federalState);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting bans statistics for federal state {} and years {}: {}", federalState, years, e.getMessage(), e);
            throw new RuntimeException("Failed to export bans statistics for federal state " + federalState + " and years " + years, e);
        }
    }

    @Override
    public RenderedContent exportStatisticsByYears(List<Integer> years, StatisticsExportType exportType) {
        try {
            log.debug("Exporting bans statistics for years: {} with export type: {}", years, exportType);

            List<BansStatistics> bansStatistics = new ArrayList<>();
            for (Integer year : years) {
                Integer issuedCount = banViewService.getIssuedAmountByYear(year);
                BansStatisticsData data = new BansStatisticsData(issuedCount);
                BansStatistics bansStatistic = new BansStatistics(year, data);
                bansStatistics.add(bansStatistic);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateBansStatisticsCsv(bansStatistics, "bans-statistics");
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting bans statistics for years {}: {}", years, e.getMessage(), e);
            throw new RuntimeException("Failed to export bans statistics for years " + years, e);
        }
    }

    private RenderedContent generateBansStatisticsCsv(List<BansStatistics> bansStatistics, String baseFilename) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Year", "Issued Bans Count")
                .build();

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            for (BansStatistics statistic : bansStatistics) {
                csvPrinter.printRecord(
                        statistic.year(),
                        statistic.data().issued()
                );
            }
        }

        String filename = baseFilename + "-" + getCurrentTimestamp();
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new RenderedContent(filename, RenderedContentType.CSV, content);
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
