package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;

import java.util.List;

public interface LicensesStatisticsExportService {

    /**
     * Exports licenses statistics for a specific license type with optional filtering.
     *
     * @param licenseType  The type of license (e.g., REGULAR, LIMITED, VACATION).
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @param exportType   The export type (e.g., CSV).
     * @return A RenderedContent object containing the exported data.
     */
    RenderedContent exportLicensesStatistics(
            LicenseType licenseType,
            List<Integer> years,
            String office,
            String federalState,
            StatisticsExportType exportType
    );
}
