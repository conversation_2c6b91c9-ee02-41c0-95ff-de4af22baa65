package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class InspectionsStatisticsExportServiceImpl implements InspectionsStatisticsExportService {

    private final InspectorProtocolService inspectorProtocolService;

    @Override
    public RenderedContent exportInspectionsStatistics(
            List<Integer> years,
            String federalState,
            StatisticsExportType exportType) {
        try {
            log.debug("Exporting inspections statistics for years: {}, federalState: {} with export type: {}",
                    years, federalState, exportType);

            List<InspectionsStatisticsResult> inspectionsStatisticsResults;

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : inspectorProtocolService.getAvailableYears();

            if (federalState != null && !federalState.isEmpty()) {
                inspectionsStatisticsResults = inspectorProtocolService.getInspectionsStatistics(yearsToQuery, federalState);
            } else {
                inspectionsStatisticsResults = inspectorProtocolService.getInspectionsStatistics(yearsToQuery, null);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateInspectionsStatisticsCsv(inspectionsStatisticsResults, federalState);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting inspections statistics for years {}, federalState {}: {}",
                    years, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export inspections statistics", e);
        }
    }

    private RenderedContent generateInspectionsStatisticsCsv(
            List<InspectionsStatisticsResult> inspectionsStatisticsResults,
            String federalState) throws IOException {

        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Year", "Active Inspectors", "Number of Inspections")
                .build();

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            for (InspectionsStatisticsResult result : inspectionsStatisticsResults) {
                csvPrinter.printRecord(
                        result.getYear(),
                        result.getData().getActiveInspectors(),
                        result.getData().getNumberOfInspections()
                );
            }
        }

        String filename = buildFilename(federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new RenderedContent(filename, RenderedContentType.CSV, content);
    }

    private String buildFilename(String federalState) {
        StringBuilder filename = new StringBuilder("inspections-statistics");
        
        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }
        
        filename.append("-").append(getCurrentTimestamp());
        
        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
