package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.statistics.StatisticsExportType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class LicensesStatisticsExportServiceImpl implements LicensesStatisticsExportService {

    private final LicensesStatisticsViewService licensesStatisticsViewService;

    @Override
    public RenderedContent exportLicensesStatistics(
            LicenseType licenseType,
            List<Integer> years,
            String office,
            String federalState,
            StatisticsExportType exportType) {
        try {
            log.debug("Exporting licenses statistics for licenseType: {}, years: {}, office: {}, federalState: {} with export type: {}",
                    licenseType, years, office, federalState, exportType);

            List<LicensesStatisticsView> licensesStatisticsViews;

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : licensesStatisticsViewService.getAvailableYears();

            // if federalState is provided the office filter is ignored
            if (federalState != null && !federalState.isEmpty()) {
                licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, yearsToQuery);
            } else if (office != null && !office.isEmpty()) {
                licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndOfficeAndYears(licenseType, office, yearsToQuery);
            } else {
                licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, yearsToQuery);
            }

            if (exportType == StatisticsExportType.CSV) {
                return generateLicensesStatisticsCsv(licensesStatisticsViews, licenseType, office, federalState);
            }

            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        } catch (Exception e) {
            log.error("Error exporting licenses statistics for licenseType {}, years {}, office {}, federalState {}: {}",
                    licenseType, years, office, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export licenses statistics", e);
        }
    }

    private RenderedContent generateLicensesStatisticsCsv(
            List<LicensesStatisticsView> licensesStatisticsViews,
            LicenseType licenseType,
            String office,
            String federalState) throws IOException {

        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Bundesland", "Jahr", "Behörde", "Scheintyp", "Antragsweg", "Anzahl")
                .build();

        // Group and aggregate the data by federalState, year, office, licenseType, source
        Map<String, Integer> aggregatedData = licensesStatisticsViews.stream()
                .collect(Collectors.groupingBy(
                        view -> view.getFederalState() + "|" + view.getYear() + "|" + view.getOffice() + "|" + view.getLicenseType() + "|" + view.getSource(),
                        Collectors.summingInt(LicensesStatisticsView::getCount)
                ));

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            // Sort by key to ensure consistent ordering
            aggregatedData.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        try {
                            String[] keyParts = entry.getKey().split("\\|");
                            csvPrinter.printRecord(
                                    keyParts[0], // federalState
                                    keyParts[1], // year
                                    keyParts[2], // office
                                    keyParts[3], // licenseType
                                    keyParts[4], // source
                                    entry.getValue() // aggregated count
                            );
                        } catch (IOException e) {
                            throw new RuntimeException("Error writing CSV record", e);
                        }
                    });
        }

        String filename = buildFilename(licenseType, office, federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new RenderedContent(filename, RenderedContentType.CSV, content);
    }

    private String buildFilename(LicenseType licenseType, String office, String federalState) {
        StringBuilder filename = new StringBuilder("licenses-statistics");
        
        if (licenseType != null) {
            filename.append("-").append(licenseType.toString().toLowerCase());
        }
        
        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }
        
        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "_"));
        }
        
        filename.append("-").append(getCurrentTimestamp());
        
        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
