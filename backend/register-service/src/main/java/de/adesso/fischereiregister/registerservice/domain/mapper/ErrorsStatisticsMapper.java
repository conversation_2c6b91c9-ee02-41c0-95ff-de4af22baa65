package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.statistics.errors_statistics.ErrorsStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default")
public interface ErrorsStatisticsMapper {

    ErrorsStatisticsMapper INSTANCE = Mappers.getMapper(ErrorsStatisticsMapper.class);

    List<org.openapitools.model.ErrorsStatistics> toResponse(List<ErrorsStatistics> list);
}
